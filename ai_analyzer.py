"""
AI Analysis Module - OpenAI API Integration for Market Analysis
"""
import json
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import pandas as pd
from openai import OpenAI
from deriv_config import DerivConfig
from logger_config import setup_logger, log_api_call, log_error

class AIMarketAnalyzer:
    """OpenAI-powered market analysis for trading signals"""

    def __init__(self):
        self.logger = setup_logger("AIAnalyzer", show_console=False)  # Hide AI logs from console
        self.client = OpenAI(api_key=DerivConfig.OPENAI_API_KEY)
        self.model = DerivConfig.OPENAI_MODEL

    def _format_market_data(self, market_data: Dict) -> str:
        """
        Format market data for AI analysis

        Args:
            market_data: Comprehensive market data dictionary

        Returns:
            Formatted string for AI prompt
        """
        formatted_data = []

        # Quote information
        if market_data.get('quote'):
            quote = market_data['quote']
            is_fx = market_data.get('is_fx', False)

            if is_fx:
                # FX quote format
                formatted_data.append(f"Current FX Quote for {quote['symbol']}:")
                formatted_data.append(f"  Exchange Rate: {quote['price']:.5f}")
                if 'bid' in quote and 'ask' in quote:
                    formatted_data.append(f"  Bid: {quote['bid']:.5f}")
                    formatted_data.append(f"  Ask: {quote['ask']:.5f}")
                    spread = quote['ask'] - quote['bid']
                    formatted_data.append(f"  Spread: {spread:.5f}")
                formatted_data.append(f"  Last Refreshed: {quote.get('last_refreshed', 'N/A')}")
                formatted_data.append(f"  Timezone: {quote.get('timezone', 'N/A')}")
            else:
                # Stock quote format
                formatted_data.append(f"Current Quote for {quote['symbol']}:")
                formatted_data.append(f"  Price: ${quote['price']:.2f}")
                if 'change' in quote and 'change_percent' in quote:
                    formatted_data.append(f"  Change: {quote['change']:+.2f} ({quote['change_percent']}%)")
                if 'volume' in quote:
                    formatted_data.append(f"  Volume: {quote['volume']:,}")
                if 'latest_trading_day' in quote:
                    formatted_data.append(f"  Latest Trading Day: {quote['latest_trading_day']}")
            formatted_data.append("")

        # Recent price action from intraday data
        if market_data.get('intraday') is not None and not market_data['intraday'].empty:
            intraday = market_data['intraday'].tail(10)  # Last 10 periods
            is_fx = market_data.get('is_fx', False)
            formatted_data.append("Recent Intraday Price Action (Last 10 periods):")

            for timestamp, row in intraday.iterrows():
                if is_fx:
                    # FX data doesn't have volume
                    formatted_data.append(f"  {timestamp}: O:{row['open']:.5f} H:{row['high']:.5f} L:{row['low']:.5f} C:{row['close']:.5f}")
                else:
                    # Stock data has volume
                    if 'volume' in row:
                        formatted_data.append(f"  {timestamp}: O:{row['open']:.2f} H:{row['high']:.2f} L:{row['low']:.2f} C:{row['close']:.2f} V:{int(row['volume']):,}")
                    else:
                        formatted_data.append(f"  {timestamp}: O:{row['open']:.2f} H:{row['high']:.2f} L:{row['low']:.2f} C:{row['close']:.2f}")
            formatted_data.append("")

        # Technical indicators
        if market_data.get('technical_indicators'):
            formatted_data.append("Technical Indicators:")
            indicators = market_data['technical_indicators']

            # Format key indicators for AI analysis
            for indicator_name, data in indicators.items():
                if data is not None and not data.empty and len(data) > 0:
                    try:
                        latest_value = data.iloc[-1]
                        if pd.notna(latest_value):
                            formatted_data.append(f"  {indicator_name}: {latest_value:.4f}")
                    except (IndexError, TypeError):
                        continue

            # Add indicator summary if available
            if market_data.get('indicator_summary'):
                summary = market_data['indicator_summary']
                formatted_data.append("")
                formatted_data.append("Technical Analysis Summary:")

                if summary.get('trend_signals'):
                    formatted_data.append("  Trend Signals:")
                    for signal in summary['trend_signals']:
                        formatted_data.append(f"    - {signal}")

                if summary.get('momentum_signals'):
                    formatted_data.append("  Momentum Signals:")
                    for signal in summary['momentum_signals']:
                        formatted_data.append(f"    - {signal}")

                if summary.get('volatility_signals'):
                    formatted_data.append("  Volatility Signals:")
                    for signal in summary['volatility_signals']:
                        formatted_data.append(f"    - {signal}")

                if summary.get('support_resistance'):
                    sr = summary['support_resistance']
                    if sr:
                        formatted_data.append("  Key Levels:")
                        for level_name, level_value in sr.items():
                            if level_value:
                                formatted_data.append(f"    {level_name}: {level_value:.5f}")

            formatted_data.append("")

        return "\n".join(formatted_data)

    def _create_analysis_prompt(self, market_data: Dict, additional_context: str = "", trade_state_context: str = "") -> str:
        """
        Create a comprehensive prompt for AI analysis

        Args:
            market_data: Market data dictionary
            additional_context: Additional context or instructions
            trade_state_context: Current trade state information

        Returns:
            Formatted prompt string
        """
        formatted_data = self._format_market_data(market_data)
        is_fx = market_data.get('is_fx', False)
        is_synthetic = market_data.get('is_synthetic', False)

        # Get timeframe information
        timeframe_info = market_data.get('timeframe', {})
        timeframe_desc = timeframe_info.get('description', 'unknown timeframe')
        timeframe_type = timeframe_info.get('type', 'UNKNOWN')
        granularity = timeframe_info.get('granularity', 60)

        # Determine asset type for binary options
        if is_synthetic:
            asset_type = "synthetic index"
        elif is_fx:
            asset_type = "FX pair"
        else:
            asset_type = "stock/commodity"

        # Customize analysis factors for binary options
        if is_synthetic:
            analysis_factors = """
1. Technical indicators analysis (RSI, MACD, moving averages, Bollinger Bands)
2. Volatility patterns and momentum indicators
3. Short-term price movements and tick analysis
4. Support and resistance levels from technical indicators
5. Overbought/oversold conditions from RSI and Stochastic
6. Trend direction from moving averages and MACD
7. Price action patterns and reversal signals
8. Bollinger Bands squeeze and expansion patterns
9. Time-based patterns and market cycles"""
        elif is_fx:
            analysis_factors = """
1. Technical indicators analysis (RSI, MACD, moving averages, Bollinger Bands)
2. Currency pair dynamics and correlations
3. Support and resistance levels from technical indicators
4. Trend analysis and breakout patterns using moving averages
5. Momentum signals from RSI and MACD
6. Volatility analysis using Bollinger Bands and ATR
7. Price action and momentum patterns
8. Economic fundamentals and central bank policies
9. Risk sentiment and volatility
10. Short-term reversal patterns for binary options"""
        else:
            analysis_factors = """
1. Technical indicators analysis (RSI, MACD, moving averages, Bollinger Bands)
2. Price action and momentum patterns
3. Support and resistance levels from technical indicators
4. Trend analysis using moving averages
5. Momentum signals from RSI and oscillators
6. Volatility analysis using Bollinger Bands and ATR
7. Volume analysis and liquidity (if available)
8. Market trends and patterns
9. Current market conditions
10. Short-term price movement patterns"""

        # Timeframe-specific analysis instructions
        if timeframe_type == "ULTRA_SHORT":
            timeframe_context = """
ULTRA SHORT-TERM ANALYSIS (Tick Data):
- Focus on immediate price momentum and micro-trends
- Look for rapid price movements and volatility spikes
- Recommended duration: 1-3 ticks
- High-frequency patterns and noise filtering important"""
        elif timeframe_type == "VERY_SHORT":
            timeframe_context = """
VERY SHORT-TERM ANALYSIS (1-minute candles):
- Focus on short-term momentum and breakouts
- Consider recent candle patterns and volume
- Recommended duration: 3-5 ticks or 1-2 minutes
- Good for scalping strategies"""
        elif timeframe_type == "SHORT":
            timeframe_context = """
SHORT-TERM ANALYSIS (5-minute candles):
- Focus on trend continuation and reversal patterns
- Consider support/resistance levels
- Recommended duration: 5-10 ticks or 3-5 minutes
- Better signal reliability than ultra-short"""
        elif timeframe_type == "MEDIUM":
            timeframe_context = """
MEDIUM-TERM ANALYSIS (15-minute candles):
- Focus on established trends and pattern formations
- Consider broader market context
- Recommended duration: 5-10 minutes
- Higher confidence but fewer opportunities"""
        elif timeframe_type == "LONG":
            timeframe_context = """
LONG-TERM ANALYSIS (1-hour candles):
- Focus on major trend direction and key levels
- Consider fundamental factors if applicable
- Recommended duration: 10 minutes
- Highest confidence for trend following"""
        else:
            timeframe_context = f"""
CUSTOM TIMEFRAME ANALYSIS ({timeframe_desc}):
- Adapt analysis to the specific timeframe characteristics
- Consider the balance between signal frequency and reliability
- Adjust duration recommendations accordingly"""

        # Binary options specific instructions
        binary_options_context = f"""

BINARY OPTIONS TRADING CONTEXT:
You are analyzing for BINARY OPTIONS trading using {timeframe_desc}.
- Focus on price direction prediction for the selected timeframe
- Consider volatility and price action patterns
- Analyze support/resistance levels for potential reversals
- Look for clear directional signals
- Binary options are time-sensitive with fixed expiry times

{timeframe_context}"""

        # Add trade state context if provided
        trade_state_instruction = ""
        if trade_state_context:
            trade_state_instruction = f"""

IMPORTANT - CURRENT TRADE STATE:
{trade_state_context}

Based on the current trade state:
- If NO POSITION: You can recommend CALL (price will rise), PUT (price will fall), or HOLD
- If CALL POSITION: Only recommend HOLD (to maintain position) or EXIT (to close early)
- If PUT POSITION: Only recommend HOLD (to maintain position) or EXIT (to close early)
- Consider the current P&L and time to expiry when deciding whether to hold or exit"""

        prompt = f"""You are an expert binary options trader and technical analyst with deep knowledge of short-term price movements, volatility analysis, and binary options strategies.

Analyze the following {asset_type} market data and provide a binary options trading recommendation:

{formatted_data}

{additional_context}{binary_options_context}{trade_state_instruction}

Please provide your analysis in the following JSON format:
{{
    "signal": "CALL" | "PUT" | "HOLD" | "EXIT",
    "confidence": 0.0-1.0,
    "reasoning": "Detailed explanation of your analysis and reasoning for binary options",
    "key_factors": ["factor1", "factor2", "factor3"],
    "risk_assessment": "LOW" | "MEDIUM" | "HIGH",
    "duration_recommendation": 1-10,
    "duration_unit": "t" | "m",
    "time_horizon": "VERY_SHORT" | "SHORT"
}}

Consider the following in your analysis:{analysis_factors}

BINARY OPTIONS SPECIFIC GUIDELINES:
- CALL signal means you predict price will be HIGHER at expiry
- PUT signal means you predict price will be LOWER at expiry
- Focus on short-term momentum and directional bias
- Consider volatility levels for duration recommendations
- Be conservative with confidence levels - binary options are high-risk
- Only recommend CALL or PUT if you have strong conviction based on technical signals
- Duration should be 1-10 ticks (t) for very short-term or 1-10 minutes (m) for short-term
- Higher volatility assets may need shorter durations

{"For synthetic indices, focus on volatility patterns and momentum." if is_synthetic else "For FX pairs, consider both currencies' strength and short-term momentum." if is_fx else "For stocks/commodities, pay attention to volume confirmation and momentum."}"""

        return prompt

    def analyze_market_data(self, market_data: Dict, additional_context: str = "", trade_state_context: str = "") -> Optional[Dict]:
        """
        Analyze market data using OpenAI and return trading signal

        Args:
            market_data: Comprehensive market data
            additional_context: Additional context for analysis
            trade_state_context: Current trade state information

        Returns:
            Analysis result dictionary or None if failed
        """
        try:
            prompt = self._create_analysis_prompt(market_data, additional_context, trade_state_context)

            self.logger.info(f"[AI] Analyzing market data for {market_data.get('symbol', 'Unknown')}")

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a professional financial analyst. Provide objective, data-driven trading analysis."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=DerivConfig.OPENAI_MAX_TOKENS,
                temperature=DerivConfig.OPENAI_TEMPERATURE
            )

            log_api_call(self.logger, "OpenAI", self.model, "success")

            # Parse the response
            content = response.choices[0].message.content.strip()

            # Try to extract JSON from the response
            try:
                # Find JSON in the response
                start_idx = content.find('{')
                end_idx = content.rfind('}') + 1

                if start_idx != -1 and end_idx != 0:
                    json_str = content[start_idx:end_idx]
                    analysis = json.loads(json_str)

                    # Validate required fields
                    required_fields = ['signal', 'confidence', 'reasoning']
                    if all(field in analysis for field in required_fields):
                        analysis['timestamp'] = datetime.now()
                        analysis['model_used'] = self.model
                        analysis['raw_response'] = content

                        self.logger.info(f"[AI] Analysis complete: {analysis['signal']} (confidence: {analysis['confidence']:.2%})")
                        return analysis
                    else:
                        self.logger.error("Missing required fields in AI response")
                        return None

                else:
                    self.logger.error("No valid JSON found in AI response")
                    return None

            except json.JSONDecodeError as e:
                self.logger.error(f"Failed to parse AI response as JSON: {e}")
                self.logger.debug(f"Raw response: {content}")
                return None

        except Exception as e:
            log_error(self.logger, e, "OpenAI API analysis")
            return None

    def get_market_sentiment(self, symbol: str, news_context: str = "") -> Optional[Dict]:
        """
        Analyze market sentiment for a given symbol

        Args:
            symbol: Stock symbol
            news_context: Recent news or market context

        Returns:
            Sentiment analysis result
        """
        try:
            prompt = f"""Analyze the current market sentiment for {symbol}.

{news_context}

Consider:
1. Overall market conditions
2. Sector performance
3. Recent news and events
4. Investor sentiment indicators

Provide your sentiment analysis in JSON format:
{{
    "sentiment": "BULLISH" | "BEARISH" | "NEUTRAL",
    "confidence": 0.0-1.0,
    "key_drivers": ["driver1", "driver2"],
    "outlook": "SHORT_TERM" | "MEDIUM_TERM" | "LONG_TERM"
}}"""

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a market sentiment analyst."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.3
            )

            content = response.choices[0].message.content.strip()

            # Parse JSON response
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1

            if start_idx != -1 and end_idx != 0:
                json_str = content[start_idx:end_idx]
                sentiment = json.loads(json_str)
                sentiment['timestamp'] = datetime.now()
                return sentiment

            return None

        except Exception as e:
            log_error(self.logger, e, "sentiment analysis")
            return None

    def validate_signal(self, analysis: Dict, market_data: Dict) -> Dict:
        """
        Validate and enhance the trading signal with additional checks

        Args:
            analysis: AI analysis result
            market_data: Market data used for analysis

        Returns:
            Enhanced analysis with validation
        """
        enhanced_analysis = analysis.copy()

        # Add validation flags
        validation_flags = []

        # Check confidence threshold
        if analysis['confidence'] < 0.6:
            validation_flags.append("LOW_CONFIDENCE")

        # Check for sufficient volume (only for stocks)
        is_fx = market_data.get('is_fx', False)
        if not is_fx and market_data.get('quote') and 'volume' in market_data['quote']:
            if market_data['quote']['volume'] < 100000:
                validation_flags.append("LOW_VOLUME")

        # Check for extreme price movements (only for stocks with change data)
        if not is_fx and market_data.get('quote') and 'change_percent' in market_data['quote']:
            change_percent = float(market_data['quote']['change_percent'].rstrip('%'))
            if abs(change_percent) > 10:
                validation_flags.append("EXTREME_MOVEMENT")

        enhanced_analysis['validation_flags'] = validation_flags
        enhanced_analysis['validated_at'] = datetime.now()

        # Adjust confidence based on validation flags
        if validation_flags:
            penalty = len(validation_flags) * 0.1
            enhanced_analysis['adjusted_confidence'] = max(0.0, analysis['confidence'] - penalty)
        else:
            enhanced_analysis['adjusted_confidence'] = analysis['confidence']

        return enhanced_analysis
