"""
Binary Options Trader
Handles binary options trading logic and execution using Deriv API
"""
import time
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass

from deriv_api import DerivAPIClient, DerivResponse
from deriv_config import DerivConfig
from deriv_market_data import DerivMarketDataProvider
from logger_config import setup_logger, log_error

class ContractType(Enum):
    CALL = "VANILLALONGCALL"    # Vanilla Call option
    PUT = "VANILLALONGPUT"      # Vanilla Put option

class ContractStatus(Enum):
    PENDING = "pending"
    OPEN = "open"
    WON = "won"
    LOST = "lost"
    SOLD = "sold"

@dataclass
class BinaryOption:
    """Represents a binary option contract"""
    contract_id: str
    symbol: str
    contract_type: ContractType
    stake: float
    duration: int
    duration_unit: str
    entry_price: float
    current_price: float
    payout: float
    status: ContractStatus
    buy_time: datetime
    expiry_time: datetime
    proposal_id: str = None
    exit_price: float = None
    profit_loss: float = 0.0

class BinaryOptionsTrader:
    """
    Binary options trading engine for Deriv
    Handles contract execution, monitoring, and risk management
    """
    
    def __init__(self):
        self.logger = setup_logger("BinaryOptionsTrader", show_console=True)  # Show trade execution logs
        self.api_client = DerivAPIClient()
        self.market_data = DerivMarketDataProvider()

        # Trading state
        self.active_contracts: Dict[str, BinaryOption] = {}
        self.trading_enabled = False
        self.daily_loss = 0.0
        self.daily_profit = 0.0
        self.trade_count = 0

        # Risk management
        self.max_concurrent_trades = DerivConfig.MAX_CONCURRENT_TRADES
        self.max_stake_per_trade = DerivConfig.MAX_STAKE_PER_TRADE
        self.max_daily_loss = DerivConfig.MAX_DAILY_LOSS
        self.min_confidence_threshold = 0.75  # 75% minimum confidence for trade execution

        # Performance tracking
        self.trade_history: List[BinaryOption] = []
        self.win_rate = 0.0
        self.total_trades = 0
        self.total_wins = 0
        
    def start(self) -> bool:
        """Start the binary options trader"""
        try:
            # Start API client and market data
            if not self.api_client.start():
                self.logger.error("Failed to start API client")
                return False
            
            if not self.market_data.start():
                self.logger.error("Failed to start market data provider")
                return False
            
            # Check authentication
            if not self.api_client.is_authenticated_user():
                self.logger.warning("Not authenticated - trading will be limited")
            
            # Validate configuration
            if not DerivConfig.validate_config():
                self.logger.error("Invalid configuration")
                return False
            
            self.trading_enabled = True
            self.logger.info("Binary options trader started successfully")
            return True
            
        except Exception as e:
            log_error(self.logger, e, "starting binary options trader")
            return False
    
    def stop(self):
        """Stop the binary options trader"""
        try:
            self.trading_enabled = False
            
            # Close any open positions if needed
            self._close_all_positions()
            
            self.market_data.stop()
            self.api_client.stop()
            
            self.logger.info("Binary options trader stopped")
            
        except Exception as e:
            log_error(self.logger, e, "stopping binary options trader")
    
    def get_proposal(self, symbol: str, contract_type: ContractType,
                    duration: int, duration_unit: str, stake: float) -> Optional[Dict]:
        """Get a price proposal for a vanilla option"""
        try:
            if not self.trading_enabled:
                self.logger.error("Trading not enabled")
                return None

            # Validate inputs
            if stake > self.max_stake_per_trade:
                self.logger.error(f"Stake {stake} exceeds maximum {self.max_stake_per_trade}")
                return None

            if len(self.active_contracts) >= self.max_concurrent_trades:
                self.logger.error(f"Maximum concurrent trades ({self.max_concurrent_trades}) reached")
                return None

            response = self.api_client.get_vanilla_proposal(
                contract_type=contract_type.value,
                symbol=symbol,
                duration=duration,
                duration_unit=duration_unit,
                stake=stake,
                barrier="+0.00"  # At-the-money strike for vanilla options
            )
            
            if response and not response.error:
                proposal = response.data.get('proposal', {})
                return {
                    'id': proposal.get('id'),
                    'ask_price': float(proposal.get('ask_price', 0)),
                    'payout': float(proposal.get('payout', 0)),
                    'spot': float(proposal.get('spot', 0)),
                    'spot_time': proposal.get('spot_time'),
                    'display_value': proposal.get('display_value')
                }
            else:
                self.logger.error(f"Failed to get proposal: {response.error if response else 'No response'}")
                return None
                
        except Exception as e:
            log_error(self.logger, e, "getting proposal")
            return None
    
    def place_trade(self, symbol: str, contract_type: ContractType, 
                   duration: int, duration_unit: str = 't', 
                   stake: float = None) -> Optional[BinaryOption]:
        """Place a binary options trade"""
        try:
            if not self.trading_enabled:
                self.logger.error("Trading not enabled")
                return None
            
            # Use default stake if not provided
            if stake is None:
                stake = DerivConfig.DEFAULT_STAKE
            
            # Risk management checks
            if not self._check_risk_limits(stake):
                return None
            
            # Get proposal
            proposal = self.get_proposal(symbol, contract_type, duration, duration_unit, stake)
            if not proposal:
                self.logger.error("Failed to get proposal")
                return None
            
            # Buy the contract
            response = self.api_client.buy_contract(proposal['id'], proposal['ask_price'])
            if response and not response.error:
                buy_data = response.data.get('buy', {})
                contract_id = buy_data.get('contract_id')
                
                if contract_id:
                    # Create binary option object
                    binary_option = BinaryOption(
                        contract_id=str(contract_id),
                        symbol=symbol,
                        contract_type=contract_type,
                        stake=stake,
                        duration=duration,
                        duration_unit=duration_unit,
                        entry_price=proposal['spot'],
                        current_price=proposal['spot'],
                        payout=proposal['payout'],
                        status=ContractStatus.OPEN,
                        buy_time=datetime.now(),
                        expiry_time=datetime.now() + timedelta(
                            minutes=duration if duration_unit == 'm' else 0,
                            seconds=duration if duration_unit == 't' else 0
                        ),
                        proposal_id=proposal['id']
                    )
                    
                    # Store active contract
                    self.active_contracts[contract_id] = binary_option
                    self.trade_count += 1
                    
                    self.logger.info(f"Placed {contract_type.value} trade on {symbol}: "
                                   f"Stake=${stake}, Payout=${proposal['payout']}, "
                                   f"Entry=${proposal['spot']}")
                    
                    return binary_option
                else:
                    self.logger.error("No contract ID in buy response")
                    return None
            else:
                self.logger.error(f"Failed to buy contract: {response.error if response else 'No response'}")
                return None
                
        except Exception as e:
            log_error(self.logger, e, "placing trade")
            return None
    
    def close_trade(self, contract_id: str) -> bool:
        """Close an open trade early"""
        try:
            if contract_id not in self.active_contracts:
                self.logger.error(f"Contract {contract_id} not found")
                return False
            
            binary_option = self.active_contracts[contract_id]
            
            # Get current portfolio to find sell price
            portfolio_response = self.api_client.get_portfolio()
            if portfolio_response and not portfolio_response.error:
                contracts = portfolio_response.data.get('portfolio', {}).get('contracts', [])
                
                for contract in contracts:
                    if str(contract.get('contract_id')) == contract_id:
                        sell_price = float(contract.get('bid_price', 0))
                        
                        if sell_price > 0:
                            # Sell the contract
                            sell_response = self.api_client.sell_contract(contract_id, sell_price)
                            if sell_response and not sell_response.error:
                                # Update contract status
                                binary_option.status = ContractStatus.SOLD
                                binary_option.exit_price = sell_price
                                binary_option.profit_loss = sell_price - binary_option.stake
                                
                                # Move to history
                                self.trade_history.append(binary_option)
                                del self.active_contracts[contract_id]
                                
                                # Update daily P&L
                                if binary_option.profit_loss > 0:
                                    self.daily_profit += binary_option.profit_loss
                                else:
                                    self.daily_loss += abs(binary_option.profit_loss)
                                
                                self.logger.info(f"Closed trade {contract_id} early: P&L=${binary_option.profit_loss:.2f}")
                                return True
                            else:
                                self.logger.error(f"Failed to sell contract: {sell_response.error if sell_response else 'No response'}")
                                return False
                        else:
                            self.logger.error("No valid sell price available")
                            return False
                
                self.logger.error(f"Contract {contract_id} not found in portfolio")
                return False
            else:
                self.logger.error("Failed to get portfolio")
                return False
                
        except Exception as e:
            log_error(self.logger, e, f"closing trade {contract_id}")
            return False
    
    def update_active_contracts(self):
        """Update status of active contracts"""
        try:
            if not self.active_contracts:
                return
            
            # Get current portfolio
            portfolio_response = self.api_client.get_portfolio()
            if portfolio_response and not portfolio_response.error:
                portfolio_contracts = portfolio_response.data.get('portfolio', {}).get('contracts', [])
                
                # Check each active contract
                for contract_id, binary_option in list(self.active_contracts.items()):
                    # Check if contract is still in portfolio (still open)
                    found_in_portfolio = False
                    for portfolio_contract in portfolio_contracts:
                        if str(portfolio_contract.get('contract_id')) == contract_id:
                            found_in_portfolio = True
                            # Update current price
                            binary_option.current_price = float(portfolio_contract.get('current_spot', binary_option.current_price))
                            break
                    
                    # If not in portfolio, contract has expired
                    if not found_in_portfolio:
                        self._handle_expired_contract(contract_id, binary_option)
                        
        except Exception as e:
            log_error(self.logger, e, "updating active contracts")
    
    def _handle_expired_contract(self, contract_id: str, binary_option: BinaryOption):
        """Handle an expired contract"""
        try:
            # Contract has expired, determine if won or lost
            # This would typically be determined by the final spot price vs entry price
            # For now, we'll mark it as completed and move to history
            
            # Calculate profit/loss (simplified - actual calculation depends on contract outcome)
            if binary_option.contract_type == ContractType.CALL:
                won = binary_option.current_price > binary_option.entry_price
            else:  # PUT
                won = binary_option.current_price < binary_option.entry_price
            
            if won:
                binary_option.status = ContractStatus.WON
                binary_option.profit_loss = binary_option.payout - binary_option.stake
                self.daily_profit += binary_option.profit_loss
                self.total_wins += 1
            else:
                binary_option.status = ContractStatus.LOST
                binary_option.profit_loss = -binary_option.stake
                self.daily_loss += binary_option.stake
            
            binary_option.exit_price = binary_option.current_price
            
            # Move to history
            self.trade_history.append(binary_option)
            del self.active_contracts[contract_id]
            
            self.total_trades += 1
            self.win_rate = (self.total_wins / self.total_trades) * 100 if self.total_trades > 0 else 0
            
            self.logger.info(f"Contract {contract_id} expired: {binary_option.status.value.upper()}, "
                           f"P&L=${binary_option.profit_loss:.2f}")
            
        except Exception as e:
            log_error(self.logger, e, f"handling expired contract {contract_id}")
    
    def _check_risk_limits(self, stake: float) -> bool:
        """Check if trade meets risk management criteria"""
        # Check stake limits
        if stake > self.max_stake_per_trade:
            self.logger.error(f"Stake {stake} exceeds maximum {self.max_stake_per_trade}")
            return False
        
        # Check daily loss limit
        if self.daily_loss >= self.max_daily_loss:
            self.logger.error(f"Daily loss limit reached: ${self.daily_loss}")
            return False
        
        # Check concurrent trades
        if len(self.active_contracts) >= self.max_concurrent_trades:
            self.logger.error(f"Maximum concurrent trades reached: {len(self.active_contracts)}")
            return False
        
        return True
    
    def _close_all_positions(self):
        """Close all open positions"""
        for contract_id in list(self.active_contracts.keys()):
            self.close_trade(contract_id)
    
    def get_trading_summary(self) -> Dict:
        """Get current trading summary"""
        return {
            'active_contracts': len(self.active_contracts),
            'total_trades': self.total_trades,
            'win_rate': self.win_rate,
            'daily_profit': self.daily_profit,
            'daily_loss': self.daily_loss,
            'net_daily_pnl': self.daily_profit - self.daily_loss,
            'trading_enabled': self.trading_enabled
        }
