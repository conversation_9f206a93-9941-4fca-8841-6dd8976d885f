"""
Binary Options Trading Bot
Main controller for binary options trading using Deriv API
"""
import time
import argparse
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import signal
import sys
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.live import Live

from deriv_config import DerivConfig
from deriv_market_data import DerivMarketDataProvider
from binary_options_trader import BinaryOptionsTrader, ContractType
from ai_analyzer import AIMarketAnalyzer
from signal_generator import SignalGenerator, SignalType
from logger_config import setup_logger, log_error

class BinaryOptionsBot:
    """Main binary options trading bot controller"""
    
    def __init__(self):
        self.logger = setup_logger("BinaryOptionsBot")
        self.console = Console()

        # Initialize components
        self.market_data = DerivMarketDataProvider()
        self.trader = BinaryOptionsTrader()
        self.ai_analyzer = AIMarketAnalyzer()
        self.signal_generator = SignalGenerator()

        # Bot state
        self.is_running = False
        self.analysis_count = 0
        self.last_analysis_time = None
        self.min_confidence_threshold = 0.75  # 75% minimum confidence for trade execution

        # Setup graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        self.logger.info("[SHUTDOWN] Shutdown signal received, stopping bot...")
        self.stop()
        sys.exit(0)
    
    def start(self) -> bool:
        """Start the binary options bot"""
        try:
            self.console.print("[bold green]Starting Binary Options Bot[/bold green]")

            # Validate configuration
            if not DerivConfig.validate_config():
                self.console.print("[red]Configuration validation failed[/red]")
                return False

            # Start components silently
            if not self.market_data.start():
                self.console.print("[red]Failed to start market data provider[/red]")
                return False

            if not self.trader.start():
                self.console.print("[red]Failed to start binary options trader[/red]")
                return False

            self.is_running = True
            self.console.print("[green]Binary Options Bot started successfully[/green]")

            return True

        except Exception as e:
            log_error(self.logger, e, "starting binary options bot")
            return False
    
    def stop(self):
        """Stop the binary options bot"""
        try:
            self.is_running = False
            self.trader.stop()
            self.market_data.stop()
            self.console.print("[yellow]Binary Options Bot stopped[/yellow]")
            
        except Exception as e:
            log_error(self.logger, e, "stopping binary options bot")

    def get_user_symbol_choice(self) -> Optional[str]:
        """Interactive symbol selection"""
        try:
            # Get available symbols from API
            symbols = self.market_data.get_active_symbols()
            if not symbols:
                # Fallback to configured symbols
                available_symbols = DerivConfig.BINARY_OPTIONS_SYMBOLS
            else:
                # Filter for binary options compatible symbols
                available_symbols = []
                for symbol_data in symbols:
                    symbol = symbol_data['symbol']
                    if symbol in DerivConfig.BINARY_OPTIONS_SYMBOLS or DerivConfig.is_synthetic_index(symbol) or DerivConfig.is_forex_symbol(symbol):
                        available_symbols.append(symbol)

                # Add configured symbols that might not be in API response
                for symbol in DerivConfig.BINARY_OPTIONS_SYMBOLS:
                    if symbol not in available_symbols:
                        available_symbols.append(symbol)

            # Display symbol selection menu
            self.console.print("\n[bold blue]Select Trading Symbol[/bold blue]")
            self.console.print("-" * 50)

            # Create symbol table
            table = Table(title="Available Symbols")
            table.add_column("No.", style="cyan", width=4)
            table.add_column("Symbol", style="white", width=10)
            table.add_column("Name", style="yellow", width=25)
            table.add_column("Type", style="green", width=15)

            for i, symbol in enumerate(available_symbols, 1):
                symbol_info = DerivConfig.get_symbol_info(symbol)
                symbol_type = "Synthetic" if DerivConfig.is_synthetic_index(symbol) else "Forex" if DerivConfig.is_forex_symbol(symbol) else "Other"
                table.add_row(str(i), symbol, symbol_info.get('name', symbol), symbol_type)

            self.console.print(table)

            # Get user choice
            while True:
                try:
                    choice = input(f"\nEnter symbol number (1-{len(available_symbols)}) or 'q' to quit: ").strip()

                    if choice.lower() == 'q':
                        return None

                    choice_num = int(choice)
                    if 1 <= choice_num <= len(available_symbols):
                        selected_symbol = available_symbols[choice_num - 1]
                        self.console.print(f"[green]Selected: {selected_symbol}[/green]")
                        return selected_symbol
                    else:
                        self.console.print(f"[red]Please enter a number between 1 and {len(available_symbols)}[/red]")

                except ValueError:
                    self.console.print("[red]Please enter a valid number or 'q' to quit[/red]")
                except KeyboardInterrupt:
                    return None

        except Exception as e:
            log_error(self.logger, e, "getting user symbol choice")
            return None

    def get_user_timeframe_choice(self) -> tuple[int, str]:
        """Interactive timeframe selection"""
        try:
            # Define available timeframes
            timeframes = [
                {"name": "1 Minute Candles", "granularity": 60, "count": 50, "description": "Short-term analysis (1min candles)"},
                {"name": "5 Minute Candles", "granularity": 300, "count": 50, "description": "Medium-term analysis (5min candles)"},
                {"name": "15 Minute Candles", "granularity": 900, "count": 50, "description": "Longer-term analysis (15min candles)"},
                {"name": "1 Hour Candles", "granularity": 3600, "count": 24, "description": "Daily trend analysis (1hr candles)"},
                {"name": "Tick Data Only", "granularity": 0, "count": 100, "description": "Ultra short-term (tick-by-tick)"}
            ]

            # Display timeframe selection menu
            self.console.print("\n[bold blue]Select Analysis Timeframe[/bold blue]")
            self.console.print("-" * 50)

            # Create timeframe table
            table = Table(title="Available Timeframes")
            table.add_column("No.", style="cyan", width=4)
            table.add_column("Timeframe", style="white", width=20)
            table.add_column("Description", style="yellow", width=30)

            for i, tf in enumerate(timeframes, 1):
                table.add_row(str(i), tf["name"], tf["description"])

            self.console.print(table)

            # Get user choice
            while True:
                try:
                    choice = input(f"\nEnter timeframe number (1-{len(timeframes)}) or 'q' to quit: ").strip()

                    if choice.lower() == 'q':
                        return None, None

                    choice_num = int(choice)
                    if 1 <= choice_num <= len(timeframes):
                        selected_tf = timeframes[choice_num - 1]
                        self.console.print(f"[green]Selected: {selected_tf['name']}[/green]")
                        return selected_tf["granularity"], selected_tf["count"]
                    else:
                        self.console.print(f"[red]Please enter a number between 1 and {len(timeframes)}[/red]")

                except ValueError:
                    self.console.print("[red]Please enter a valid number or 'q' to quit[/red]")
                except KeyboardInterrupt:
                    return None, None

        except Exception as e:
            log_error(self.logger, e, "getting user timeframe choice")
            return None, None

    def analyze_symbol(self, symbol: str, context: str = "", granularity: int = 60, candle_count: int = 50) -> Optional[Dict]:
        """
        Analyze a symbol for binary options trading

        Args:
            symbol: Symbol to analyze
            context: Additional context for analysis
            granularity: Timeframe granularity in seconds (0 for tick data only)
            candle_count: Number of candles to retrieve

        Returns:
            Analysis result or None if failed
        """
        try:
            self.logger.info(f"Starting binary options analysis for {symbol} (timeframe: {granularity}s)")

            # Check if connected with retry mechanism (silent)
            max_retries = 3
            for attempt in range(max_retries):
                if self.market_data.is_connected():
                    break
                if attempt < max_retries - 1:
                    time.sleep(2)  # Wait silently
                else:
                    self.console.print("[red]ERROR: Not connected to Deriv API[/red]")
                    return None

            # Get market data based on timeframe
            tick_history = self.market_data.get_tick_history(symbol, count=100)

            # Get candle data with technical indicators only if granularity > 0
            candle_data_with_indicators = None
            if granularity > 0:
                candle_data_with_indicators = self.market_data.get_candle_history_with_indicators(symbol, granularity=granularity, count=candle_count)

            latest_tick = self.market_data.get_latest_tick(symbol)
            
            if tick_history is None or tick_history.empty:
                self.console.print(f"[red]ERROR: No tick data available for {symbol}[/red]")
                return None

            # Determine timeframe description for AI context
            if granularity == 0:
                timeframe_desc = "tick-by-tick data"
                timeframe_type = "ULTRA_SHORT"
            elif granularity == 60:
                timeframe_desc = "1-minute candles"
                timeframe_type = "VERY_SHORT"
            elif granularity == 300:
                timeframe_desc = "5-minute candles"
                timeframe_type = "SHORT"
            elif granularity == 900:
                timeframe_desc = "15-minute candles"
                timeframe_type = "MEDIUM"
            elif granularity == 3600:
                timeframe_desc = "1-hour candles"
                timeframe_type = "LONG"
            else:
                timeframe_desc = f"{granularity}-second candles"
                timeframe_type = "CUSTOM"

            # Prepare market data for AI analysis
            market_data = {
                'symbol': symbol,
                'timestamp': datetime.now(),
                'timeframe': {
                    'granularity': granularity,
                    'description': timeframe_desc,
                    'type': timeframe_type,
                    'candle_count': candle_count
                },
                'quote': {
                    'symbol': symbol,
                    'price': latest_tick['price'] if latest_tick else tick_history.iloc[-1]['price'],
                    'timestamp': latest_tick['timestamp'] if latest_tick else tick_history.index[-1],
                    'bid': latest_tick.get('bid', 0) if latest_tick else 0,
                    'ask': latest_tick.get('ask', 0) if latest_tick else 0
                },
                'intraday': candle_data_with_indicators['candle_data'] if candle_data_with_indicators else tick_history.tail(20),
                'tick_history': tick_history,
                'candle_history': candle_data_with_indicators['candle_data'] if candle_data_with_indicators else None,
                'technical_indicators': candle_data_with_indicators['technical_indicators'] if candle_data_with_indicators else {},
                'indicator_summary': candle_data_with_indicators['indicator_summary'] if candle_data_with_indicators else {},
                'is_synthetic': DerivConfig.is_synthetic_index(symbol),
                'is_fx': DerivConfig.is_forex_symbol(symbol),
                'symbol_info': DerivConfig.get_symbol_info(symbol)
            }
            
            # Get current trade state for context
            current_state = self.signal_generator.get_current_trade_state(symbol)
            entry_price = self.signal_generator.entry_prices.get(symbol, 0.0)
            
            trade_state_context = f"Current state: {current_state.value}"
            if entry_price > 0:
                current_price = market_data['quote']['price']
                pnl_pct = ((current_price - entry_price) / entry_price) * 100
                trade_state_context += f", Entry: {entry_price:.5f}, Current: {current_price:.5f}, P&L: {pnl_pct:+.2f}%"
            
            # AI analysis
            ai_analysis = self.ai_analyzer.analyze_market_data(
                market_data, 
                additional_context=context,
                trade_state_context=trade_state_context
            )
            
            if not ai_analysis:
                self.console.print(f"[red]ERROR: AI analysis failed for {symbol}[/red]")
                return None
            
            # Validate and enhance the analysis
            validated_analysis = self.ai_analyzer.validate_signal(ai_analysis, market_data)
            
            # Generate trading signal
            trading_signal = self.signal_generator.generate_signal(validated_analysis, market_data)
            
            if trading_signal:
                self.analysis_count += 1
                self.last_analysis_time = datetime.now()
                
                # Display signal
                self._display_signal(trading_signal, market_data)
                
                # Execute trade if signal is actionable and meets confidence threshold
                if trading_signal.signal in [SignalType.CALL, SignalType.PUT]:
                    if trading_signal.confidence >= self.min_confidence_threshold:
                        self._execute_binary_option(trading_signal)
                    else:
                        self.console.print(f"[yellow]Signal confidence ({trading_signal.confidence:.1%}) below threshold ({self.min_confidence_threshold:.0%}) - Trade not executed[/yellow]")
            
            return {
                'signal': trading_signal,
                'market_data': market_data,
                'ai_analysis': validated_analysis
            }
            
        except Exception as e:
            log_error(self.logger, e, f"analyzing {symbol}")
            return None
    
    def _execute_binary_option(self, signal):
        """Execute a binary options trade based on signal"""
        try:
            if signal.signal == SignalType.CALL:
                contract_type = ContractType.CALL
            elif signal.signal == SignalType.PUT:
                contract_type = ContractType.PUT
            else:
                return

            # Place the trade
            binary_option = self.trader.place_trade(
                symbol=signal.symbol,
                contract_type=contract_type,
                duration=signal.duration or DerivConfig.DEFAULT_DURATION,
                duration_unit=signal.duration_unit or DerivConfig.DEFAULT_DURATION_UNIT,
                stake=DerivConfig.DEFAULT_STAKE
            )

            if binary_option:
                self.console.print(f"[green]Trade executed: {contract_type.value} {signal.symbol}[/green]")
            else:
                self.console.print(f"[red]Failed to execute trade[/red]")

        except Exception as e:
            log_error(self.logger, e, "executing binary option")
            self.console.print(f"[red]Trade execution error: {str(e)}[/red]")
    
    def _display_signal(self, signal, market_data):
        """Display trading signal in console"""
        try:
            # Create signal display table
            table = Table(title=f"Binary Options Signal - {signal.symbol}")
            table.add_column("Property", style="cyan")
            table.add_column("Value", style="white")
            
            # Signal color based on type
            signal_color = "green" if signal.signal in [SignalType.CALL, SignalType.PUT] else "yellow"
            
            table.add_row("Signal", f"[{signal_color}]{signal.signal.value}[/{signal_color}]")
            table.add_row("Confidence", f"{signal.confidence:.1%}")
            table.add_row("Current Price", f"{market_data['quote']['price']:.5f}")
            table.add_row("Risk Level", signal.risk_level.value)
            
            if signal.duration:
                table.add_row("Duration", f"{signal.duration} {signal.duration_unit}")
            
            if signal.validation_flags:
                table.add_row("Flags", ", ".join(signal.validation_flags))
            
            self.console.print(table)
            
            # Display reasoning
            reasoning_panel = Panel(
                signal.reasoning,
                title="Analysis Reasoning",
                border_style="blue"
            )
            self.console.print(reasoning_panel)
            
        except Exception as e:
            log_error(self.logger, e, "displaying signal")
    
    def run_single_analysis(self, symbol: str = None, context: str = ""):
        """Run a single analysis cycle with interactive selection"""
        # Interactive symbol selection if not provided
        if not symbol:
            symbol = self.get_user_symbol_choice()
            if not symbol:
                self.console.print("[yellow]Analysis cancelled by user[/yellow]")
                return

        # Interactive timeframe selection
        granularity, candle_count = self.get_user_timeframe_choice()
        if granularity is None:
            self.console.print("[yellow]Analysis cancelled by user[/yellow]")
            return

        # Display analysis header
        timeframe_desc = "tick data" if granularity == 0 else f"{granularity//60}min candles" if granularity >= 60 else f"{granularity}s candles"

        self.console.print(f"\n[bold blue]Binary Options Analysis[/bold blue]")
        self.console.print(f"Symbol: {symbol}")
        self.console.print(f"Timeframe: {timeframe_desc}")
        self.console.print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.console.print("-" * 60)

        result = self.analyze_symbol(symbol, context, granularity, candle_count)
        
        if result:
            # Update active contracts
            self.trader.update_active_contracts()
            
            # Display trading summary
            summary = self.trader.get_trading_summary()
            self.console.print(f"\n[bold]Trading Summary:[/bold]")
            self.console.print(f"Active Contracts: {summary['active_contracts']}")
            self.console.print(f"Total Trades: {summary['total_trades']}")
            self.console.print(f"Win Rate: {summary['win_rate']:.1f}%")
            self.console.print(f"Daily P&L: ${summary['net_daily_pnl']:.2f}")
        
        return result
    
    def run_continuous(self, symbols: List[str] = None, interval_minutes: int = 1):
        """Run bot continuously with scheduled analysis"""
        symbols = symbols or DerivConfig.BINARY_OPTIONS_SYMBOLS[:3]  # Limit to 3 symbols
        
        self.console.print(f"\n[bold green]Starting Continuous Binary Options Trading[/bold green]")
        self.console.print(f"Symbols: {', '.join(symbols)}")
        self.console.print(f"Interval: {interval_minutes} minutes")
        self.console.print("-" * 60)
        
        try:
            while self.is_running:
                for symbol in symbols:
                    if not self.is_running:
                        break
                    
                    self.analyze_symbol(symbol)
                    
                    # Update active contracts
                    self.trader.update_active_contracts()
                    
                    # Brief pause between symbols
                    time.sleep(5)
                
                # Wait for next cycle
                if self.is_running:
                    self.console.print(f"[dim]Waiting {interval_minutes} minutes for next cycle...[/dim]")
                    time.sleep(interval_minutes * 60)
                    
        except KeyboardInterrupt:
            self.console.print("\n[yellow]Stopping continuous trading...[/yellow]")
        except Exception as e:
            log_error(self.logger, e, "continuous trading")

def main():
    """Main entry point with interactive menu"""
    # Create and start bot
    bot = BinaryOptionsBot()

    if not bot.start():
        print("Failed to start binary options bot")
        return

    try:
        # Interactive main menu
        while True:
            bot.console.print("\n[bold green]Binary Options Trading Bot[/bold green]")
            bot.console.print("-" * 50)
            bot.console.print("1. Single Analysis (Interactive)")
            bot.console.print("2. Continuous Trading")
            bot.console.print("3. Exit")

            choice = input("\nSelect option (1-3): ").strip()

            if choice == "1":
                bot.run_single_analysis()
            elif choice == "2":
                # Get symbols for continuous trading
                symbols = []
                bot.console.print("\n[bold blue]Select symbols for continuous trading:[/bold blue]")
                while True:
                    symbol = bot.get_user_symbol_choice()
                    if symbol:
                        symbols.append(symbol)
                        add_more = input("Add another symbol? (y/n): ").strip().lower()
                        if add_more != 'y':
                            break
                    else:
                        break

                if symbols:
                    interval = input("Enter analysis interval in minutes (default 1): ").strip()
                    try:
                        interval = int(interval) if interval else 1
                    except ValueError:
                        interval = 1

                    bot.console.print(f"[green]Starting continuous trading with {len(symbols)} symbols...[/green]")
                    bot.run_continuous(symbols, interval)
                else:
                    bot.console.print("[yellow]No symbols selected for continuous trading[/yellow]")
            elif choice == "3":
                bot.console.print("[yellow]Exiting...[/yellow]")
                break
            else:
                bot.console.print("[red]Invalid choice. Please select 1, 2, or 3.[/red]")

    except KeyboardInterrupt:
        bot.console.print("\n[yellow]Interrupted by user[/yellow]")
    finally:
        bot.stop()

if __name__ == "__main__":
    main()
